﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NPOI" Version="2.6.2" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="app.Designer.cs">
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <AutoGen>True</AutoGen>
      <DependentUpon>app.config</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <None Update="app.config">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>app.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>

</Project>
