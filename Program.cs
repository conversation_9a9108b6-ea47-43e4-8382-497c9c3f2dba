using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Configuration;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;
using System.Drawing.Printing;
using System.Reflection.Emit;
namespace TemplateReplacerAdvanced
{
    class Program
    {
        static string filepath = ConfigurationManager.AppSettings["filepath"];
        static readonly HttpClient client = new HttpClient();

        // 从配置文件读取API端点
        static string parentTaskApiUrl = ConfigurationManager.AppSettings["ParentTaskApiUrl"];
        static string childTaskApiUrl = ConfigurationManager.AppSettings["ChildTaskApiUrl"];
        static string timeZone = ConfigurationManager.AppSettings["TimeZone"];
        static string Preview = ConfigurationManager.AppSettings["Preview"];
        static string Origin = ConfigurationManager.AppSettings["origin"];
        static string cookie = ConfigurationManager.AppSettings["Cookie"];
        static async Task Main(string[] args)
        {
            // 如果配置文件中没有Cookie，提示用户输入
            if (string.IsNullOrEmpty(cookie))
            {
                Console.WriteLine("请输入Cookie（在浏览器开发者工具中获取）：");
                cookie = Console.ReadLine();
            }

            try
            {
                // 模板文件路径
                string parentTemplatePath = filepath + "\\父任务-模版.json";
                // 定义子任务模板参数
                var childTaskParams = new[]
                {
                    new { Title = "有促-POS", IsPromotion = 1, IsPOS = true },
                    new { Title = "有促-非POS", IsPromotion = 1, IsPOS = false },
                    new { Title = "无促-非POS", IsPromotion = 0, IsPOS = false },
                    new { Title = "无促-POS", IsPromotion = 0, IsPOS = true }
                };
                // 定义positionTitle参数 500900002, 100000051, 100000024  "Supervisor","Supervisor without Promoters","Merchandiser"
                var positionTitleParams = new[]
                {
                    new { PositionId = 500900002, Title = "Supervisor", Label= "Supervisor" },
                    new { PositionId = 100000051, Title = "Supervisor without Promoters", Label = "SupervisorWP" },
                    new { PositionId = 100000024, Title = "Merchandiser", Label = "Merchandiser" }
                };
                // 定义positionList常量
                var POS_POSITIONS = new[] { 100000005 };
                var NON_POS_POSITIONS = new[] { 100000002, 100000006, 100000004, 100000003 };

                // 读取父模板和基础子模板
                string parentTemplate = File.ReadAllText(parentTemplatePath, Encoding.UTF8);
                string baseChildTemplate = File.ReadAllText(filepath + "\\子任务模版.json", Encoding.UTF8);

                // 从Excel读取数据文件
                string excelPath = filepath + "\\数据.xlsx";
                using var fs = new FileStream(excelPath, FileMode.Open, FileAccess.Read);
                var workbook = new XSSFWorkbook(fs);
                var sheet = workbook.GetSheetAt(0);

                // 获取标题行（列名）
                var headerRow = sheet.GetRow(0);
                var headers = headerRow.Cells.Select(c => c.StringCellValue).ToArray();

                // 设置时区信息
                TimeZoneInfo utc8TimeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZone);

                // 处理每一行数据（跳过标题行）
                for (int i = 1; i <= sheet.LastRowNum; i++)
                {
                    var row = sheet.GetRow(i);
                    if (row == null)
                    {
                        Console.WriteLine($"警告：第{i + 1}行为空，跳过此行。");
                        continue;
                    }

                    // 检查单元格数量是否匹配
                    if (row.LastCellNum != headers.Length)
                    {
                        Console.WriteLine($"警告：第{i + 1}行数据列数与标题不匹配，跳过此行。");
                        continue;
                    }

                    // 创建当前行的数据字典
                    var data = new Dictionary<string, string>();
                    for (int j = 0; j < headers.Length; j++)
                    {
                        var cell = row.GetCell(j);
                        string cellValue = "";

                        if (cell != null)
                        {
                            switch (cell.CellType)
                            {
                                case CellType.String:
                                    cellValue = cell.StringCellValue;
                                    break;
                                case CellType.Numeric:
                                    if (DateUtil.IsCellDateFormatted(cell))
                                        cellValue = cell.DateCellValue.ToString();
                                    else
                                        cellValue = cell.NumericCellValue.ToString();
                                    break;
                                case CellType.Boolean:
                                    cellValue = cell.BooleanCellValue.ToString();
                                    break;
                                case CellType.Formula:
                                    switch (cell.CachedFormulaResultType)
                                    {
                                        case CellType.String:
                                            cellValue = cell.StringCellValue;
                                            break;
                                        case CellType.Numeric:
                                            cellValue = cell.NumericCellValue.ToString();
                                            break;
                                        default:
                                            cellValue = "";
                                            break;
                                    }
                                    break;
                                default:
                                    cellValue = "";
                                    break;
                            }
                        }

                        data[headers[j]] = cellValue.Replace(";", ",");
                    }

                    // 验证必要字段
                    var requiredFields = new[] {
                        "countrycode", "countryname", "countryname_en",
                        "storeGradeList", "pushtitle", "taskDuration", "frequency",
                        "effectiveTime"
                    };

                    var missingFields = requiredFields.Where(field => !data.ContainsKey(field) || string.IsNullOrWhiteSpace(data[field]));
                    if (missingFields.Any())
                    {
                        Console.WriteLine($"警告：第{i + 1}行数据缺少必要字段: {string.Join(", ", missingFields)}，跳过此行。");
                        continue;
                    }

                    // 验证数值字段
                    try
                    {
                        long.Parse(data["taskDuration"]);
                        DateTime.Parse(data["effectiveTime"]);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"警告：第{i + 1}行数据的数值字段格式错误：{ex.Message}，跳过此行。");
                        continue;
                    }

                    // 设置UTC+8时间并转换为时间戳(毫秒)
                    DateTime utc8DateTime = DateTime.Parse(data["effectiveTime"]);
                    DateTime utc8AdjustedTime = TimeZoneInfo.ConvertTimeToUtc(utc8DateTime, utc8TimeZone);
                    long unixTimeMs = ((DateTimeOffset)utc8AdjustedTime).ToUnixTimeMilliseconds();
                    data["effectiveTimeStamp"] = unixTimeMs.ToString();

                    // 处理父模板并调用API
                    var parentJsonObject = JArray.Parse(parentTemplate).First;
                    var parentJsonCopy = JObject.Parse(parentJsonObject.ToString(Formatting.None));

                    // 设置任务名称
                    var taskName = positionTitleParams.FirstOrDefault(p => data["positionTitleList"].Contains(p.PositionId.ToString()))?.Label ?? "Unknown";

                    // 更新父任务的字段
                    parentJsonCopy["title"] = $"{data["countryname"]}-{taskName}-{data["storeGradeList"]}";
                    parentJsonCopy["pushTitle"] = $"{data["countryname"]}-{taskName}-{data["storeGradeList"]}";
                    parentJsonCopy["pushPeriod"]["effectiveTimeStamp"] = long.Parse(data["effectiveTimeStamp"]);
                    parentJsonCopy["pushPeriod"]["effectiveTime"] = data["effectiveTime"];
                    parentJsonCopy["taskDuration"] = long.Parse(data["taskDuration"]);
                    parentJsonCopy["frequency"] = long.Parse(data["frequency"]);

                    // 更新国家相关信息
                    var countrycodeList = data["countrycode"].Split(',')
                        .Select(grade => grade.Trim())
                        .Where(grade => !string.IsNullOrEmpty(grade))
                        .ToArray();
                    parentJsonCopy["channelRetail"]["areaList"] = JArray.FromObject(countrycodeList);
                    parentJsonCopy["area"] = JArray.FromObject(countrycodeList).First.ToString();

                    var areaNameList = data["countryname_en"].Split(',')
                        .Select(grade => grade.Trim())
                        .Where(grade => !string.IsNullOrEmpty(grade))
                        .ToArray();
                    parentJsonCopy["channelRetail"]["areaNameList"] = JArray.FromObject(areaNameList);

                    // 更新门店等级列表
                    var gradeList = data["storeGradeList"].Split(',')
                        .Select(grade => grade.Trim())
                        .Where(grade => !string.IsNullOrEmpty(grade))
                        .ToArray();
                    parentJsonCopy["channelRetail"]["storeGradeList"] = JArray.FromObject(gradeList);

                    // 更新职位列表
                    var titleList = positionTitleParams
                        .Where(p => data["positionTitleList"].Contains(p.PositionId.ToString()))
                        .Select(p => p.PositionId)
                        .ToArray();
                    var titleListNames = positionTitleParams
                    .Where(p => data["positionTitleList"].Contains(p.PositionId.ToString()))
                    .Select(p => p.Title)
                    .ToArray();
                    parentJsonCopy["channelRetail"]["positionTitleList"] = JArray.FromObject(titleList);
                    parentJsonCopy["channelRetail"]["positionTitleNameList"] = JArray.FromObject(titleListNames);

                    var parentTaskJson = $"[{parentJsonCopy}]";



                    // 保存文件
                    string parentOutputFileName = $"{data["countryname"]}-{taskName}-{data["storeGradeList"]}级.json";
                    File.WriteAllText($"{filepath}\\output\\" + parentOutputFileName, parentTaskJson, Encoding.UTF8);
                    Console.WriteLine($"已生成文件: {parentOutputFileName}");

                    // 调用父任务API并获取ID
                    string parentTaskId = await CallParentTaskApi(parentTaskJson);
                    //string parentTaskId = "0";
                    Console.WriteLine($"父任务创建成功，ID: {parentTaskId}");

                    // 如果获取到父任务ID，则处理子任务
                    if (!string.IsNullOrEmpty(parentTaskId))
                    {
                        // 添加父任务ID到数据字典
                        data["parentTaskId"] = parentTaskId;
                        data["parentTaskDefinitionId"] = parentTaskId; // 设置子任务的parentTaskDefinitionId字段

                        // 处理每个子任务参数组合
                        foreach (var param in childTaskParams)
                        {
                            // 复制并解析基础模板
                            var jsonObject = JArray.Parse(baseChildTemplate).First;
                            var jsonCopy = JObject.Parse(jsonObject.ToString(Formatting.None));

                            // 更新子任务的字段
                            jsonCopy["pushPeriod"]["effectiveTimeStamp"] = long.Parse(data["effectiveTimeStamp"]);
                            jsonCopy["pushPeriod"]["effectiveTime"] = data["effectiveTime"];
                            jsonCopy["parentTaskDefinitionId"] = parentTaskId;
                            jsonCopy["title"] = $"{data["countryname"]}-{taskName}-{data["storeGradeList"]}-{(param.IsPromotion == 1 ? "PC" : "NPC")}-{(param.IsPOS ? "POS" : "NPOS")}";
                            jsonCopy["pushTitle"] = $"{data["countryname"]}-{taskName}-{data["storeGradeList"]}-{(param.IsPromotion == 1 ? "PC" : "NPC")}-{(param.IsPOS ? "POS" : "NPOS")}";
                            jsonCopy["taskDuration"] = long.Parse(data["taskDuration"]);
                            jsonCopy["frequency"] = long.Parse(data["frequency"]);

                            // 更新国家相关信息
                            jsonCopy["channelRetail"]["areaList"] = JArray.FromObject(countrycodeList);
                            jsonCopy["channelRetail"]["areaNameList"] = JArray.FromObject(areaNameList);
                            jsonCopy["area"] = JArray.FromObject(countrycodeList).First.ToString();

                            // 更新门店等级列表
                            jsonCopy["channelRetail"]["storeGradeList"] = JArray.FromObject(gradeList);

                            // 设置渠道信息
                            jsonCopy["channelRetail"]["isPromotion"] = param.IsPromotion;
                            jsonCopy["channelRetail"]["positionList"] = JArray.FromObject(
                                param.IsPOS ? POS_POSITIONS : NON_POS_POSITIONS
                            );
                            jsonCopy["channelRetail"]["positionNameList"] = JArray.FromObject(
                                param.IsPOS ? new[] { "POS" } : new[] { "SIS", "DC", "DZ", "ES" }
                            );
                            jsonCopy["channelRetail"]["positionTitleList"] = JArray.FromObject(titleList);
                            jsonCopy["channelRetail"]["positionTitleNameList"] = JArray.FromObject(titleListNames);


                            // 设置事件定义ID
                            string eventKey = param.IsPromotion == 1
                                ? (param.IsPOS ? "eventDefinitionIdpcpos" : "eventDefinitionIdpcnpos")
                                : (param.IsPOS ? "eventDefinitionIdnpcpos" : "eventDefinitionIdnpcnpos");

                            if (data.ContainsKey(eventKey))
                            {
                                jsonCopy["eventDefinitionIds"] = JArray.Parse($"[{data[eventKey]}]");
                            }
                            // 判断eventDefinitionIds是否为空
                            if (jsonCopy["eventDefinitionIds"] == null || !jsonCopy["eventDefinitionIds"].Any())
                            {
                                Console.WriteLine("eventDefinitionIds为空，无法创建子任务");
                                continue;
                            }

                            var childTaskJson = $"[{jsonCopy}]";
                            var childOutputFileName = $"{data["countryname"]}-{taskName}-{data["storeGradeList"]}级-子任务-{(param.IsPromotion == 1 ? "有促" : "无促")}-{(param.IsPOS ? "POS" : "非POS")}.json";

                            // 保存子任务文件
                            File.WriteAllText($"{filepath}\\output\\" + childOutputFileName, childTaskJson, Encoding.UTF8);
                            Console.WriteLine($"已生成文件: {childOutputFileName}");

                            // 调用子任务API
                            await CallChildTaskApi(childTaskJson);
                            Console.WriteLine($"子任务创建成功,{data["countryname"]}-{taskName}-{data["storeGradeList"]}级-子任务-{(param.IsPromotion == 1 ? "有促" : "无促")}-{(param.IsPOS ? "POS" : "非POS")}");
                        }
                    }
                }

                Console.WriteLine("所有任务创建完成！");
                Console.ReadLine();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
                Console.ReadLine();
            }
        }

        // 调用父任务API
        static async Task<string> CallParentTaskApi(string jsonPayload)
        {
            try
            {
                // 准备HTTP请求
                var request = new HttpRequestMessage(HttpMethod.Post, parentTaskApiUrl);

                // 添加请求头
                AddHeaders(request);

                // 设置请求体
                request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                // 发送请求
                HttpResponseMessage response = await client.SendAsync(request);

                // 确保请求成功
                response.EnsureSuccessStatusCode();

                // 读取响应内容
                string responseBody = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"父任务API响应: {responseBody}");

                // 解析响应获取任务ID
                var responseJson = JObject.Parse(responseBody);
                if (responseJson["data"] != null && responseJson["data"]["id"] != null)
                {
                    string taskId = responseJson["data"]["id"].ToString();
                    Console.WriteLine($"成功解析父任务ID: {taskId}");
                    return taskId;
                }

                Console.WriteLine("响应中未找到ID字段，响应内容: " + responseBody);
                return null;
            }
            catch (HttpRequestException e)
            {
                Console.WriteLine($"调用父任务API时发生错误: {e.Message}");
                return null;
            }
            catch (JsonException e)
            {
                Console.WriteLine($"解析API响应时发生错误: {e.Message}");
                return null;
            }
        }

        // 调用子任务API
        static async Task<string> CallChildTaskApi(string jsonPayload)
        {
            try
            {
                // 准备HTTP请求
                var request = new HttpRequestMessage(HttpMethod.Post, childTaskApiUrl);

                // 添加请求头
                AddHeaders(request);

                // 设置请求体
                request.Content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                // 发送请求
                HttpResponseMessage response = await client.SendAsync(request);

                // 确保请求成功
                response.EnsureSuccessStatusCode();

                // 读取响应内容
                string responseBody = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"子任务API响应: {responseBody}");

                // 解析响应获取任务ID（假设子任务API响应格式与父任务API类似）
                var responseJson = JObject.Parse(responseBody);
                if (responseJson["data"] != null)
                {
                    Console.WriteLine($"成功解析子任务ID");
                    return null;
                }
                Console.WriteLine("响应中未找到ID字段，响应内容: " + responseBody);
                return null;
            }
            catch (HttpRequestException e)
            {
                Console.WriteLine($"调用子任务API时发生错误: {e.Message}");
                return null;
            }
            catch (JsonException e)
            {
                Console.WriteLine($"解析API响应时发生错误: {e.Message}");
                return null;
            }
        }

        // 添加HTTP请求头
        static void AddHeaders(HttpRequestMessage request)
        {
            request.Headers.Add("accept", "application/json, text/plain, */*");
            request.Headers.Add("accept-language", "zh-CN,zh;q=0.9");
            request.Headers.Add("origin", Origin);
            request.Headers.Add("priority", "u=1, i");
            request.Headers.Add("referer", Origin);
            request.Headers.Add("sec-ch-ua", "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"");
            request.Headers.Add("sec-ch-ua-mobile", "?0");
            request.Headers.Add("sec-ch-ua-platform", "\"macOS\"");
            request.Headers.Add("sec-fetch-dest", "empty");
            request.Headers.Add("sec-fetch-mode", "cors");
            request.Headers.Add("sec-fetch-site", "same-origin");
            request.Headers.Add("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36");
            request.Headers.Add("x-retail-appsign", "CHANNEL_RETAIL");
            request.Headers.Add("x-retail-language", "en-US");
            request.Headers.Add("x-retail-locale", "CN");
            request.Headers.Add("x-retail-tenantid", "2");
            request.Headers.Add("Cookie", cookie);
            if (Preview != null && Preview == "1")
            {
                request.Headers.Add("PREVIEW-USER", "1");
            }
        }
    }
}
