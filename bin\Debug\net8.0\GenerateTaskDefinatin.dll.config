﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <appSettings>
    <!-- 模板文件路径 -->
    <add key="filepath" value="C:\Users\<USER>\Downloads\督导任务体系" />

    <!-- 任务定义创建URL 新加坡 -->
     <!-- <add key="ParentTaskApiUrl" value="http://sgp-xmmionegw-inner.be.mi.com/mtop/proretail-pc/cr/brain/platform/admin/addOrEditTaskDefinitionReturnId" />
    <add key="ChildTaskApiUrl" value="http://sgp-xmmionegw-inner.be.mi.com/mtop/proretail-pc/cr/brain/platform/admin/addOrEditTaskDefinition" /> -->
    <!-- 任务定义创建URL 欧洲 -->
    <add key="ParentTaskApiUrl" value="http://eur.xmmionegw.b2c.srv/mtop/proretail-pc/cr/brain/platform/admin/addOrEditTaskDefinitionReturnId" />
    <add key="ChildTaskApiUrl" value="http://eur.xmmionegw.b2c.srv/mtop/proretail-pc/cr/brain/platform/admin/addOrEditTaskDefinition" />
    <!-- 任务定义创建URL 测试 -->
    <!-- <add key="ParentTaskApiUrl" value="https://work-test.i.mi.com/sg/mtop/proretail-pc/cr/brain/platform/admin/addOrEditTaskDefinitionReturnId" />
    <add key="ChildTaskApiUrl" value="https://work-test.i.mi.com/sg/mtop/proretail-pc/cr/brain/platform/admin/addOrEditTaskDefinition" /> -->

    <!-- 默认时区, 无需修改 -->
    <add key="TimeZone" value="China Standard Time" />

    <!-- 是否预发环境(仅新加坡预发需要设置) -->
    <add key="Preview" value="0" />

    <!-- PC端域名 正式 -->
    <add key="origin" value="https://work.i.mi.com" />
    <!-- PC端域名 测试 -->
    <!-- <add key="origin" value="https://work-test.i.mi.com" /> -->

     <!-- PC端token, 从浏览器中获取 -->
    <add key="Cookie" value='upc_nr_token_outer=905a445151b72e2c2e99dc62eb2ba8772b039d2ac5a24630; _mode_upc_nr_token=passport' />
  </appSettings>
</configuration>
