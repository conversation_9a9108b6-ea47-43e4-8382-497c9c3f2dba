{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/win-x64": {"GenerateTaskDefinatin/1.0.0": {"dependencies": {"NPOI": "2.6.2", "Newtonsoft.Json": "13.0.3", "System.Configuration.ConfigurationManager": "6.0.0", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "8.0.5"}, "runtime": {"GenerateTaskDefinatin.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.5": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "********", "fileVersion": "13.0.524.21615"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "8.0.524.21615"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.38.33135.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "8.0.524.21615"}, "clretwrc.dll": {"fileVersion": "8.0.524.21615"}, "clrgc.dll": {"fileVersion": "8.0.524.21615"}, "clrjit.dll": {"fileVersion": "8.0.524.21615"}, "coreclr.dll": {"fileVersion": "8.0.524.21615"}, "createdump.exe": {"fileVersion": "8.0.524.21615"}, "hostfxr.dll": {"fileVersion": "8.0.524.21615"}, "hostpolicy.dll": {"fileVersion": "8.0.524.21615"}, "mscordaccore.dll": {"fileVersion": "8.0.524.21615"}, "mscordaccore_amd64_amd64_8.0.524.21615.dll": {"fileVersion": "8.0.524.21615"}, "mscordbi.dll": {"fileVersion": "8.0.524.21615"}, "mscorrc.dll": {"fileVersion": "8.0.524.21615"}, "msquic.dll": {"fileVersion": "*******"}}}, "BouncyCastle.Cryptography/2.2.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.1.47552"}}}, "Enums.NET/4.0.1": {"runtime": {"lib/netcoreapp3.0/Enums.NET.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MathNet.Numerics.Signed/4.15.0": {"runtime": {"lib/netstandard2.0/MathNet.Numerics.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"runtime": {"lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "NPOI/2.6.2": {"dependencies": {"BouncyCastle.Cryptography": "2.2.1", "Enums.NET": "4.0.1", "MathNet.Numerics.Signed": "4.15.0", "Microsoft.IO.RecyclableMemoryStream": "2.3.2", "SharpZipLib": "1.3.3", "SixLabors.Fonts": "1.0.0", "SixLabors.ImageSharp": "2.1.4", "System.Configuration.ConfigurationManager": "6.0.0", "System.Security.Cryptography.Xml": "6.0.1"}, "runtime": {"lib/net6.0/NPOI.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OOXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OpenXml4Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OpenXmlFormats.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpZipLib/1.3.3": {"runtime": {"lib/netstandard2.1/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SixLabors.Fonts/1.0.0": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SixLabors.ImageSharp/2.1.4": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Configuration.ConfigurationManager/6.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Formats.Asn1/6.0.0": {}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Pkcs/6.0.1": {"dependencies": {"System.Formats.Asn1": "6.0.0"}, "runtime": {"runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21309"}}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Cryptography.Xml/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.1"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.822.36306"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Text.Encoding.CodePages/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}}}, "libraries": {"GenerateTaskDefinatin/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.5": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "BouncyCastle.Cryptography/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-A6Zr52zVqJKt18ZBsTnX0qhG0kwIQftVAjLmszmkiR/trSp8H+xj1gUOzk7XHwaKgyREMSV1v9XaKrBUeIOdvQ==", "path": "bouncycastle.cryptography/2.2.1", "hashPath": "bouncycastle.cryptography.2.2.1.nupkg.sha512"}, "Enums.NET/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OUGCd5L8zHZ61GAf436G0gf/H6yrSUkEpV5vm2CbCUuz9Rx7iLFLP5iHSSfmOtqNpuyo4vYte0CvYEmPZXRmRQ==", "path": "enums.net/4.0.1", "hashPath": "enums.net.4.0.1.nupkg.sha512"}, "MathNet.Numerics.Signed/4.15.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFjukMRatkg9dgRM7U/gM4uKgaWAX7E0lt3fsVDTPdtBIVuh7uPlksDie290br1/tv1a4Ar/Bz9ywCPSL8PhHg==", "path": "mathnet.numerics.signed/4.15.0", "hashPath": "mathnet.numerics.signed.4.15.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-Oh1qXXFdJFcHozvb4H6XYLf2W0meZFuG0A+TfapFPj9z5fd4vxiARGEhAaLj/6XWQaMYIv4SH/9Q6H78Hw0E2Q==", "path": "microsoft.io.recyclablememorystream/2.3.2", "hashPath": "microsoft.io.recyclablememorystream.2.3.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "NPOI/2.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-s5lxJQ1Xy2nr3yDvoMH6og2cb2I8reIrUROf2afjKucS+pWNZG07kwITo+CCS3KaXNiPjUn1YaS1PUf8fT9cHg==", "path": "npoi/2.6.2", "hashPath": "npoi.2.6.2.nupkg.sha512"}, "SharpZipLib/1.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-N8+hwhsKZm25tDJfWpBSW7EGhH/R7EMuiX+KJ4C4u+fCWVc1lJ5zg1u3S1RPPVYgTqhx/C3hxrqUpi6RwK5+Tg==", "path": "sharpziplib/1.3.3", "hashPath": "sharpziplib.1.3.3.nupkg.sha512"}, "SixLabors.Fonts/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFQsCZlV0xlUyXAOMUo5kkSl+8zAQXXbbdwWchtk0B4o7zotZhQsQOcJUELGHdfPfm/xDAsz6hONAuV25bJaAg==", "path": "sixlabors.fonts/1.0.0", "hashPath": "sixlabors.fonts.1.0.0.nupkg.sha512"}, "SixLabors.ImageSharp/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-K6F39YCm/MaOYT0iw9lMa8tBcz2eF8JaNMneo0EnRrml6k+8EQNSKXqb8yJTq2HHkWLlRHZl6UKMn02YmR/G3g==", "path": "sixlabors.imagesharp/2.1.4", "hashPath": "sixlabors.imagesharp.2.1.4.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "path": "system.configuration.configurationmanager/6.0.0", "hashPath": "system.configuration.configurationmanager.6.0.0.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Formats.Asn1/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T6fD00dQ3NTbPDy31m4eQUwKW84s03z0N2C8HpOklyeaDgaJPa/TexP4/SkORMSOwc7WhKifnA6Ya33AkzmafA==", "path": "system.formats.asn1/6.0.0", "hashPath": "system.formats.asn1.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ynmbW2GjIGg9K1wXmVIRs4IlyDolf0JXNpzFQ8JCVgwM+myUC2JeUggl2PwQig2PNVMegKmN1aAx7WPQ8tI3vA==", "path": "system.security.cryptography.pkcs/6.0.1", "hashPath": "system.security.cryptography.pkcs.6.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "path": "system.security.cryptography.xml/6.0.1", "hashPath": "system.security.cryptography.xml.6.0.1.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "path": "system.text.encoding.codepages/5.0.0", "hashPath": "system.text.encoding.codepages.5.0.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"]}}