{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\SOImport\\SOImport\\ConsoleApp1\\GenerateTaskDefinatin.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\SOImport\\SOImport\\ConsoleApp1\\GenerateTaskDefinatin.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\SOImport\\SOImport\\ConsoleApp1\\GenerateTaskDefinatin.csproj", "projectName": "GenerateTaskDefinatin", "projectPath": "C:\\Users\\<USER>\\Downloads\\SOImport\\SOImport\\ConsoleApp1\\GenerateTaskDefinatin.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\SOImport\\SOImport\\ConsoleApp1\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://nrm.recloud.com.cn:8081/repository/nuget-group/": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.cdn.azure.cn/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"NPOI": {"target": "Package", "version": "[2.6.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.205/PortableRuntimeIdentifierGraph.json"}}}}}